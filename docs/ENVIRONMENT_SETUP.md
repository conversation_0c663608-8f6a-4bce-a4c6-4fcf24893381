# DevEco Studio 环境变量配置

## 配置概述
已成功为您配置了DevEco Studio的ohpm和hvigor工具环境变量。

## 配置详情

### 工具路径
- **DevEco Studio安装路径**: `/Applications/DevEco-Studio.app/Contents/tools`
- **ohpm工具路径**: `/Applications/DevEco-Studio.app/Contents/tools/ohpm/bin`
- **hvigor工具路径**: `/Applications/DevEco-Studio.app/Contents/tools/hvigor/bin`

### 环境变量配置
已在 `~/.zshrc` 文件中添加以下配置：

```bash
# DevEco Studio Tools - ohpm (已存在)
export PATH="/Applications/DevEco-Studio.app/Contents/tools/ohpm/bin:$PATH"

# DevEco Studio Tools - hvigor (新添加)
export PATH="/Applications/DevEco-Studio.app/Contents/tools/hvigor/bin:$PATH"
```

## 工具版本信息

### ohpm (OpenHarmony Package Manager)
- **版本**: 5.0.16
- **用途**: HarmonyOS包管理工具
- **命令示例**:
  ```bash
  ohpm --version          # 查看版本
  ohpm install           # 安装依赖
  ohpm list              # 列出已安装包
  ```

### hvigor (HarmonyOS Build Tool)
- **版本**: 5.17.2
- **用途**: HarmonyOS构建工具
- **命令示例**:
  ```bash
  hvigorw --version      # 查看版本
  hvigorw clean          # 清理构建
  hvigorw assembleHap    # 构建HAP包
  ```

## 验证结果

### ✅ 路径验证
```bash
$ which ohpm
/Applications/DevEco-Studio.app/Contents/tools/ohpm/bin/ohpm

$ which hvigorw
/Applications/DevEco-Studio.app/Contents/tools/hvigor/bin/hvigorw
```

### ✅ 版本验证
```bash
$ ohpm --version
5.0.16

$ hvigorw --version
5.17.2
```

## 使用说明

### 1. 立即生效
配置已自动生效，您可以在当前终端中直接使用这些命令。

### 2. 新终端窗口
新打开的终端窗口会自动加载这些环境变量。

### 3. 手动重新加载
如果需要在当前终端重新加载配置：
```bash
source ~/.zshrc
```

## 常用命令

### ohpm 包管理
```bash
# 初始化项目
ohpm init

# 安装依赖
ohpm install

# 安装特定包
ohpm install <package-name>

# 列出依赖
ohpm list

# 更新依赖
ohpm update
```

### hvigor 构建
```bash
# 清理项目
hvigorw clean

# 构建项目
hvigorw assembleHap

# 构建并运行
hvigorw assembleHap --mode module

# 查看帮助
hvigorw --help
```

## 项目构建测试

现在您可以在项目目录中测试构建：

```bash
# 进入项目目录
cd /Users/<USER>/DevEcoStudioProjects/MyApplication22

# 安装依赖
ohpm install

# 构建项目
hvigorw assembleHap
```

## 故障排除

### 如果命令找不到
1. 检查路径是否正确：
   ```bash
   ls /Applications/DevEco-Studio.app/Contents/tools/ohpm/bin
   ls /Applications/DevEco-Studio.app/Contents/tools/hvigor/bin
   ```

2. 重新加载配置：
   ```bash
   source ~/.zshrc
   ```

3. 检查PATH变量：
   ```bash
   echo $PATH | grep DevEco
   ```

### 如果权限问题
确保工具有执行权限：
```bash
chmod +x /Applications/DevEco-Studio.app/Contents/tools/ohpm/bin/ohpm
chmod +x /Applications/DevEco-Studio.app/Contents/tools/hvigor/bin/hvigorw
```

## 配置完成 ✅

环境变量配置已完成，您现在可以在任何目录下使用 `ohpm` 和 `hvigorw` 命令了！

### 下一步建议
1. 在项目目录中运行 `ohpm install` 安装依赖
2. 使用 `hvigorw assembleHap` 构建项目
3. 验证图标更新是否正常工作
