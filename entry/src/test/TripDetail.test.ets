import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

// 导入需要测试的模型和组件
import { 
  Trip, 
  DailyItinerary, 
  Activity, 
  ActivityType, 
  QuickAction, 
  QuickActionType,
  TripDataManager,
  SAMPLE_TRIP_DETAILS,
  QUICK_ACTIONS
} from '../main/ets/models/TripModel';

export default function tripDetailTest() {
  describe('TripDetail', function () {
    let tripManager: TripDataManager;

    beforeEach(function () {
      tripManager = new TripDataManager();
    });

    it('should get trip details correctly', function () {
      const tripId = 1; // 巴黎浪漫之旅
      const details = tripManager.getTripDetails(tripId);

      expect(details).assertInstanceOf('Array');
      expect(details.length).assertLarger(0);

      // 检查第一天的行程
      if (details.length > 0) {
        const firstDay = details[0];
        expect(firstDay.dayNumber).assertEqual(1);
        expect(firstDay.date).assertEqual('2024-07-15');
        expect(firstDay.title).assertEqual('抵达巴黎');
        expect(firstDay.activities).assertInstanceOf('Array');
        expect(firstDay.activities.length).assertLarger(0);
      }
    });

    it('should return empty array for non-existent trip', function () {
      const tripId = 999; // 不存在的行程ID
      const details = tripManager.getTripDetails(tripId);
      
      expect(details).assertInstanceOf('Array');
      expect(details.length).assertEqual(0);
    });

    it('should get quick actions correctly', function () {
      const quickActions = tripManager.getQuickActions();
      
      expect(quickActions).assertInstanceOf('Array');
      expect(quickActions.length).assertEqual(6);
      
      // 检查快速操作的结构
      const firstAction = quickActions[0];
      expect(firstAction.id).assertInstanceOf('String');
      expect(firstAction.title).assertInstanceOf('String');
      expect(firstAction.icon).assertInstanceOf('String');
      expect(firstAction.type).assertInstanceOf('String');
      expect(firstAction.enabled).assertInstanceOf('Boolean');
    });

    it('should validate activity structure', function () {
      const details = tripManager.getTripDetails(1);
      const firstActivity = details[0].activities[0];
      
      expect(firstActivity.id).assertInstanceOf('Number');
      expect(firstActivity.title).assertInstanceOf('String');
      expect(firstActivity.startTime).assertInstanceOf('String');
      expect(firstActivity.endTime).assertInstanceOf('String');
      expect(firstActivity.type).assertInstanceOf('String');
      expect(firstActivity.completed).assertInstanceOf('Boolean');
    });

    it('should update activity status correctly', function () {
      const tripId = 1;
      const activityId = 1;
      
      // 更新活动状态为完成
      tripManager.updateActivityStatus(tripId, activityId, true);
      
      // 验证状态已更新
      const details = tripManager.getTripDetails(tripId);
      const activity = details[0].activities.find(a => a.id === activityId);
      expect(activity?.completed).assertEqual(true);
      
      // 恢复状态
      tripManager.updateActivityStatus(tripId, activityId, false);
      const updatedDetails = tripManager.getTripDetails(tripId);
      const updatedActivity = updatedDetails[0].activities.find(a => a.id === activityId);
      expect(updatedActivity?.completed).assertEqual(false);
    });

    it('should validate activity types', function () {
      const validTypes = [
        ActivityType.SIGHTSEEING,
        ActivityType.DINING,
        ActivityType.SHOPPING,
        ActivityType.TRANSPORTATION,
        ActivityType.ACCOMMODATION,
        ActivityType.ENTERTAINMENT,
        ActivityType.OTHER
      ];
      
      expect(validTypes.length).assertEqual(7);
      expect(validTypes.includes(ActivityType.SIGHTSEEING)).assertEqual(true);
      expect(validTypes.includes(ActivityType.DINING)).assertEqual(true);
    });

    it('should validate quick action types', function () {
      const validActionTypes = [
        QuickActionType.DAILY_ITINERARY,
        QuickActionType.TRAVEL_MEMORIES,
        QuickActionType.RESTAURANT_BOOKING,
        QuickActionType.PACKING_LIST,
        QuickActionType.EXPENSE_MANAGEMENT,
        QuickActionType.ADD_ACTIVITY
      ];
      
      expect(validActionTypes.length).assertEqual(6);
      expect(validActionTypes.includes(QuickActionType.DAILY_ITINERARY)).assertEqual(true);
      expect(validActionTypes.includes(QuickActionType.ADD_ACTIVITY)).assertEqual(true);
    });

    it('should have correct sample data structure', function () {
      const tripDetails = SAMPLE_TRIP_DETAILS.getTripDetails(1);
      expect(tripDetails).assertInstanceOf('Array');
      expect(tripDetails.length).assertLarger(0);

      // 检查每日行程结构
      if (tripDetails.length > 0) {
        const day = tripDetails[0];
        expect(day.date).assertInstanceOf('String');
        expect(day.dayNumber).assertInstanceOf('Number');
        expect(day.title).assertInstanceOf('String');
        expect(day.activities).assertInstanceOf('Array');
      }
    });

    it('should have all required quick actions', function () {
      const requiredActions = [
        'daily_itinerary',
        'travel_memories', 
        'restaurant_booking',
        'packing_list',
        'expense_management',
        'add_activity'
      ];
      
      const actionIds = QUICK_ACTIONS.map(action => action.id);
      
      requiredActions.forEach(requiredId => {
        expect(actionIds.includes(requiredId)).assertEqual(true);
      });
    });
  });
}
