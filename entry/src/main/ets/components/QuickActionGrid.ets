/**
 * 快速操作网格组件
 * 显示6个快速操作按钮
 */

import { QuickAction, QuickActionType } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';

@Component
export struct QuickActionGrid {
  @Prop quickActions: QuickAction[];
  onActionClick?: (action: QuickAction) => void;

  build() {
    Column() {
      Text('快速操作')
        .fontSize(16)
        .fontWeight(600)
        .fontColor(THEME_COLORS.textPrimary)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      Grid() {
        ForEach(this.quickActions, (action: QuickAction) => {
          GridItem() {
            Column() {
              Text(action.icon)
                .fontSize(24)
                .margin({ bottom: 8 })

              Text(action.title)
                .fontSize(12)
                .fontColor(THEME_COLORS.textSecondary)
                .textAlign(TextAlign.Center)
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
            }
            .width('100%')
            .height(80)
            .justifyContent(FlexAlign.Center)
            .backgroundColor(THEME_COLORS.cardBackground)
            .borderRadius(12)
            .shadow({ radius: 1, color: 'rgba(0, 0, 0, 0.05)', offsetY: 1 })
            .onClick(() => {
              if (this.onActionClick) {
                this.onActionClick(action);
              } else {
                console.log(`点击了快速操作: ${action.title}`);
              }
            })
            .opacity(action.enabled ? 1 : 0.5)
          }
        })
      }
      .columnsTemplate('1fr 1fr 1fr')
      .rowsTemplate('1fr 1fr')
      .columnsGap(12)
      .rowsGap(12)
      .width('100%')
      .height(172)
    }
    .width('100%')
    .padding(16)
    .backgroundColor(THEME_COLORS.background)
  }
}
