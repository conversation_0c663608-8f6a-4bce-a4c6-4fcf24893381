/**
 * 行程卡片组件
 * 用于显示单个行程的详细信息
 */

import { Trip } from '../models/TripModel';
import {
  getStatusLabel,
  getStatusColor,
  getTripTypeLabel,
  formatDateRange,
  THEME_COLORS
} from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

@Component
export struct TripCard {
  @Prop trip: Trip;
  onTripClick?: (trip: Trip) => void;

  build() {
    Column() {
      // Trip Header with Title and Badges
      Row() {
        Column() {
          Text(this.trip.title)
            .fontSize(18)
            .fontWeight(600)
            .fontColor(THEME_COLORS.textPrimary)
            .alignSelf(ItemAlign.Start)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Row() {
            IconText({
              iconText: IconConstants.LOCATION,
              fontSize: 12,
              fontColor: THEME_COLORS.textSecondary,
              iconWidth: 12,
              iconHeight: 12
            })
              .margin({ right: 4 })

            Text(this.trip.destination)
              .fontSize(14)
              .fontColor(THEME_COLORS.textSecondary)
          }
          .alignItems(VerticalAlign.Center)
          .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        // Status and Type Badges
        Row() {
          Text(getStatusLabel(this.trip.status))
            .fontSize(12)
            .fontWeight(500)
            .fontColor(Color.White)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(getStatusColor(this.trip.status))
            .borderRadius(12)

          Text(getTripTypeLabel(this.trip.tripType))
            .fontSize(12)
            .fontWeight(500)
            .fontColor(THEME_COLORS.primary)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(THEME_COLORS.primaryLight)
            .borderRadius(12)
            .margin({ left: 6 })
        }
      }
      .width('100%')
      .alignItems(VerticalAlign.Top)
      .margin({ bottom: 12 })

      // Date Range
      Row() {
        IconText({
          iconText: IconConstants.CALENDAR,
          fontSize: 14,
          fontColor: THEME_COLORS.textSecondary,
          iconWidth: 14,
          iconHeight: 14
        })
          .margin({ right: 6 })

        Text(`${formatDateRange(this.trip.startDate, this.trip.endDate)}`)
          .fontSize(14)
          .fontColor(THEME_COLORS.textSecondary)

        Text(`${this.trip.daysCount}天`)
          .fontSize(14)
          .fontColor(THEME_COLORS.textSecondary)
          .margin({ left: 8 })

        Blank()

        Text(`${this.trip.progress}%`)
          .fontSize(14)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textSecondary)
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
      .margin({ bottom: 8 })

      // Progress Bar
      Column() {
        Row() {
          Text('进度')
            .fontSize(14)
            .fontColor(THEME_COLORS.textSecondary)

          Blank()

          Text(`${this.trip.progress}%`)
            .fontSize(14)
            .fontWeight(600)
            .fontColor(getStatusColor(this.trip.status))
        }
        .width('100%')
        .margin({ bottom: 6 })

        Progress({ value: this.trip.progress, total: 100, type: ProgressType.Linear })
          .width('100%')
          .height(4)
          .color(getStatusColor(this.trip.status))
          .backgroundColor('#f2f2f7')
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(THEME_COLORS.cardBackground)
    .borderRadius(12)
    .shadow({ radius: 2, color: 'rgba(0, 0, 0, 0.08)', offsetY: 1 })
    .onClick(() => {
      if (this.onTripClick) {
        this.onTripClick(this.trip);
      } else {
        console.log(`点击了行程: ${this.trip.title}`);
      }
    })
  }
}
