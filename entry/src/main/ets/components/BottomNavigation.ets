/**
 * 底部导航栏组件
 * 提供应用的主要导航功能
 */

import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

// 导航项接口
export interface NavItem {
  index: number;
  title: string;
  iconText: string;
}

// 默认导航项
export const DEFAULT_NAV_ITEMS: NavItem[] = [
  { index: 0, title: '首页', iconText: IconConstants.HOME },
  { index: 1, title: '行程', iconText: IconConstants.CALENDAR },
  { index: 2, title: '发现', iconText: IconConstants.SEARCH },
  { index: 3, title: '收藏', iconText: IconConstants.HEART },
  { index: 4, title: '我的', iconText: IconConstants.PERSON }
];

@Component
export struct BottomNavigation {
  @Prop currentTabIndex: number = 1;
  @Prop navItems: NavItem[] = DEFAULT_NAV_ITEMS;
  onTabChange?: (index: number, title: string) => void;

  @Builder
  buildTabItem(item: NavItem) {
    Column() {
      IconText({
        iconText: item.iconText,
        fontSize: 20,
        fontColor: this.currentTabIndex === item.index ? THEME_COLORS.primary : THEME_COLORS.secondary,
        iconWidth: 24,
        iconHeight: 24
      })

      Text(item.title)
        .fontSize(12)
        .fontColor(this.currentTabIndex === item.index ? THEME_COLORS.primary : THEME_COLORS.secondary)
        .margin({ top: 4 })
    }
    .width(60)
    .height(60)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      if (this.onTabChange) {
        this.onTabChange(item.index, item.title);
      } else {
        console.log(`切换到标签: ${item.title}`);
      }
    })
  }

  build() {
    Row() {
      ForEach(this.navItems, (item: NavItem) => {
        this.buildTabItem(item)
      })
    }
    .width('100%')
    .height(80)
    .backgroundColor(THEME_COLORS.cardBackground)
    .justifyContent(FlexAlign.SpaceEvenly)
    .alignItems(VerticalAlign.Center)
    .position({ x: 0, y: '100%' })
    .translate({ x: 0, y: -80 })
    .border({ width: { top: 0.5 }, color: THEME_COLORS.border })
  }
}
