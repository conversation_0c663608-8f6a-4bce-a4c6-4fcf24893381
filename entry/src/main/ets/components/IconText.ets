/**
 * 文本图标组件
 * 使用Unicode符号作为图标，确保兼容性
 */

@Component
export struct IconText {
  @Prop iconText: string = '';
  @Prop fontSize: number = 16;
  @Prop fontColor: string = '#000000';
  @Prop iconWidth: number = 24;
  @Prop iconHeight: number = 24;

  build() {
    Text(this.iconText)
      .fontSize(this.fontSize)
      .fontColor(this.fontColor)
      .width(this.iconWidth)
      .height(this.iconHeight)
      .textAlign(TextAlign.Center)
  }
}

// 图标常量定义
export class IconConstants {
  // 底部导航图标
  static readonly HOME = '🏠';
  static readonly CALENDAR = '📅';
  static readonly SEARCH = '🔍';
  static readonly HEART = '❤️';
  static readonly PERSON = '👤';
  
  // 功能图标
  static readonly LOCATION = '📍';
  static readonly FILTER = '⚙️';
  static readonly ADD = '+';
  static readonly BACK = '←';
  static readonly TIME = '🕐';

  // 状态图标
  static readonly EMPTY_SEARCH = '🔍';
  static readonly EMPTY_FILTER = '⚙️';
  static readonly EMPTY_DATA = '📅';
}
