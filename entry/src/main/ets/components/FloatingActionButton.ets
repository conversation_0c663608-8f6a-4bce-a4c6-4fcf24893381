/**
 * 浮动操作按钮组件
 * 提供快速操作的浮动按钮
 */

import { THEME_COLORS } from '../utils/TripUtils';

@Component
export struct FloatingActionButton {
  @Prop icon: string = '+';
  @Prop buttonSize: number = 56;
  @Prop buttonBackgroundColor: string = THEME_COLORS.primary;
  @Prop iconColor: string = '#ffffff';
  @Prop bottomPosition: number = 160;
  @Prop rightPosition: number = 80;
  onButtonClick?: () => void;

  build() {
    Button() {
      Text(this.icon)
        .fontSize(24)
        .fontWeight(600)
        .fontColor(this.iconColor)
    }
    .width(this.buttonSize)
    .height(this.buttonSize)
    .backgroundColor(this.buttonBackgroundColor)
    .borderRadius(this.buttonSize / 2)
    .position({ x: '100%', y: '100%' })
    .translate({ x: -this.rightPosition, y: -this.bottomPosition })
    .shadow({ radius: 12, color: 'rgba(20, 184, 166, 0.3)', offsetY: 4 })
    .onClick(() => {
      if (this.onButtonClick) {
        this.onButtonClick();
      } else {
        console.log('浮动按钮被点击');
      }
    })
  }
}
