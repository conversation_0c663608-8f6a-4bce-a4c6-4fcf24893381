/**
 * 行程安排列表组件
 * 显示每日行程安排和活动列表
 */

import { DailyItinerary, Activity, ActivityType } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

@Component
export struct ItineraryList {
  @Prop itineraries: DailyItinerary[];
  onActivityClick?: (activity: Activity) => void;

  // 获取活动类型图标
  getActivityIcon(type: ActivityType): string {
    switch (type) {
      case ActivityType.SIGHTSEEING:
        return '🏛️';
      case ActivityType.DINING:
        return '🍽️';
      case ActivityType.SHOPPING:
        return '🛍️';
      case ActivityType.TRANSPORTATION:
        return '🚗';
      case ActivityType.ACCOMMODATION:
        return '🏨';
      case ActivityType.ENTERTAINMENT:
        return '🎭';
      default:
        return '📍';
    }
  }

  // 格式化时间
  formatTime(time: string): string {
    return time;
  }

  build() {
    Column() {
      Row() {
        Text('行程概览')
          .fontSize(16)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)

        Button('编辑')
          .fontSize(14)
          .fontColor(THEME_COLORS.primary)
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: THEME_COLORS.primary, radius: 16 })
          .padding({ left: 12, right: 12, top: 6, bottom: 6 })
          .onClick(() => {
            console.log('点击编辑行程');
          })
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
      .margin({ bottom: 16 })

      List({ space: 16 }) {
        ForEach(this.itineraries, (day: DailyItinerary) => {
          ListItem() {
            Column() {
              // 日期标题
              Row() {
                // 圆形数字标识
                Stack() {
                  Circle({ width: 24, height: 24 })
                    .fill(THEME_COLORS.primary)

                  Text(day.dayNumber.toString())
                    .fontSize(12)
                    .fontWeight(600)
                    .fontColor(Color.White)
                }
                .width(24)
                .height(24)

                Column() {
                  Text(day.title)
                    .fontSize(14)
                    .fontWeight(600)
                    .fontColor(THEME_COLORS.textPrimary)
                    .alignSelf(ItemAlign.Start)

                  Text(`${day.date.replace(/-/g, '/')} • ${day.activities.length}个活动`)
                    .fontSize(12)
                    .fontColor(THEME_COLORS.textSecondary)
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 2 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                .margin({ left: 12 })

                Button('...')
                  .fontSize(16)
                  .fontColor(THEME_COLORS.textSecondary)
                  .backgroundColor(Color.Transparent)
                  .width(32)
                  .height(32)
                  .onClick(() => {
                    console.log(`点击第${day.dayNumber}天菜单`);
                  })
              }
              .width('100%')
              .alignItems(VerticalAlign.Center)
              .margin({ bottom: 12 })

              // 活动列表
              Column() {
                ForEach(day.activities, (activity: Activity, index: number) => {
                  Row() {
                    // 时间线
                    Column() {
                      Circle({ width: 8, height: 8 })
                        .fill(activity.completed ? THEME_COLORS.success : THEME_COLORS.textSecondary)

                      if (index < day.activities.length - 1) {
                        Line()
                          .width(1)
                          .height(40)
                          .stroke(THEME_COLORS.border)
                          .margin({ top: 4 })
                      }
                    }
                    .width(20)
                    .alignItems(HorizontalAlign.Center)

                    // 活动内容
                    Column() {
                      Row() {
                        Text(this.getActivityIcon(activity.type))
                          .fontSize(16)
                          .margin({ right: 8 })

                        Column() {
                          Text(activity.title)
                            .fontSize(14)
                            .fontWeight(500)
                            .fontColor(THEME_COLORS.textPrimary)
                            .alignSelf(ItemAlign.Start)

                          if (activity.description) {
                            Text(activity.description)
                              .fontSize(12)
                              .fontColor(THEME_COLORS.textSecondary)
                              .alignSelf(ItemAlign.Start)
                              .margin({ top: 2 })
                          }

                          Row() {
                            if (activity.location) {
                              IconText({
                                iconText: IconConstants.LOCATION,
                                fontSize: 12,
                                fontColor: THEME_COLORS.textSecondary,
                                iconWidth: 12,
                                iconHeight: 12
                              })
                                .margin({ right: 4 })

                              Text(activity.location)
                                .fontSize(12)
                                .fontColor(THEME_COLORS.textSecondary)
                                .margin({ right: 12 })
                            }

                            IconText({
                              iconText: IconConstants.TIME,
                              fontSize: 12,
                              fontColor: THEME_COLORS.textSecondary,
                              iconWidth: 12,
                              iconHeight: 12
                            })
                              .margin({ right: 4 })

                            Text(`${this.formatTime(activity.startTime)} - ${this.formatTime(activity.endTime)}`)
                              .fontSize(12)
                              .fontColor(THEME_COLORS.textSecondary)
                          }
                          .alignItems(VerticalAlign.Center)
                          .margin({ top: 4 })
                        }
                        .layoutWeight(1)
                        .alignItems(HorizontalAlign.Start)
                      }
                      .width('100%')
                      .alignItems(VerticalAlign.Top)
                    }
                    .layoutWeight(1)
                    .padding({ left: 12 })
                    .onClick(() => {
                      if (this.onActivityClick) {
                        this.onActivityClick(activity);
                      } else {
                        console.log(`点击了活动: ${activity.title}`);
                      }
                    })
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)
                  .margin({ bottom: index < day.activities.length - 1 ? 8 : 0 })
                })
              }
              .width('100%')
            }
            .width('100%')
            .padding(16)
            .backgroundColor(THEME_COLORS.cardBackground)
            .borderRadius(12)
            .shadow({ radius: 2, color: 'rgba(0, 0, 0, 0.08)', offsetY: 1 })
          }
        })
      }
      .width('100%')
      .layoutWeight(1)

      // 添加新的一天按钮
      Button() {
        Row() {
          Text('➕')
            .fontSize(16)
            .margin({ right: 8 })

          Text('添加新的一天')
            .fontSize(14)
            .fontColor(THEME_COLORS.primary)
        }
        .alignItems(VerticalAlign.Center)
      }
      .type(ButtonType.Normal)
      .backgroundColor(Color.Transparent)
      .border({ width: 1, color: THEME_COLORS.border, radius: 8, style: BorderStyle.Dashed })
      .width('100%')
      .height(48)
      .margin({ top: 16 })
      .onClick(() => {
        console.log('点击添加新的一天');
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, bottom: 16 })
    .backgroundColor(THEME_COLORS.background)
  }
}
